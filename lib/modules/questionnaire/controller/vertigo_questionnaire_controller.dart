import 'package:get/get.dart';
import 'package:gt_plus/enums/questionnaire_type_enum.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'base_questionnaire_controller.dart';
import '../view/vertigo_questionnaire_view.dart';

class VertigoQuestionnaireController extends BaseQuestionnaireController {
  @override
  QuestionnaireType get questionnaireType => QuestionnaireType.vertigoQuest;

  @override
  String get questionnaireKey => 'setFive'; // Vertigo is in setFive based on the remote config data

  @override
  String get routeName => VertigoQuestionnaireView.routeName;

  // Observable for tracking radio button answers
  final RxMap<String, String> radioAnswers = <String, String>{}.obs;
  
  // Observable for tracking checkbox answers
  final RxMap<String, List<String>> checkboxAnswers = <String, List<String>>{}.obs;

  // Update radio button answer
  void updateRadioAnswer(String questionId, String value) {
    radioAnswers[questionId] = value;
    updateAnswer(questionId, value);
  }

  // Update checkbox answer
  void updateCheckboxAnswer(String questionId, String value, bool isSelected) {
    if (!checkboxAnswers.containsKey(questionId)) {
      checkboxAnswers[questionId] = <String>[];
    }

    final currentAnswers = checkboxAnswers[questionId]!;
    if (isSelected) {
      if (!currentAnswers.contains(value)) {
        currentAnswers.add(value);
      }
    } else {
      currentAnswers.remove(value);
    }

    checkboxAnswers[questionId] = currentAnswers;

    // Only update the answer if there are selected values
    // This prevents sending empty arrays to the API for optional questions
    if (currentAnswers.isNotEmpty) {
      updateAnswer(questionId, currentAnswers);
    } else {
      // Remove the answer from the base controller if no options are selected
      removeAnswer(questionId);
    }
  }

  // Check if a checkbox option is selected
  bool isCheckboxSelected(String questionId, String value) {
    return checkboxAnswers[questionId]?.contains(value) ?? false;
  }

  // Get radio button answer
  String? getRadioAnswer(String questionId) {
    return radioAnswers[questionId];
  }

  @override
  bool validateAnswers() {
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.questionnaires == null) return false;

    // Check if all required questions are answered
    for (final questionnaire in questionnaireSet!.questionnaires!) {
      final questionId = questionnaire.value;

      if (questionnaire.option?.type == 'radio') {
        if (!radioAnswers.containsKey(questionId) || radioAnswers[questionId]!.isEmpty) {
          reusableSnackBar(message: 'Please answer all questions');
          return false;
        }
      }
      // Note: Checkbox questions are treated as optional
      // They are only included in the API payload if the user selects at least one option
      // If no options are selected, the question is not sent to the API
    }

    return true;
  }

  @override
  Future<void> loadSavedAnswers() async {
    await super.loadSavedAnswers();

    // Load saved radio answers
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.questionnaires != null) {
      for (final questionnaire in questionnaireSet!.questionnaires!) {
        final questionId = questionnaire.value;
        final savedAnswer = getAnswer(questionId);

        if (savedAnswer != null) {
          if (questionnaire.option?.type == 'radio') {
            radioAnswers[questionId] = savedAnswer.toString();
          } else if (questionnaire.option?.type == 'checkbox') {
            if (savedAnswer is List && savedAnswer.isNotEmpty) {
              checkboxAnswers[questionId] = savedAnswer.cast<String>();
            } else if (savedAnswer is String && savedAnswer.isNotEmpty) {
              // Handle case where checkbox answer might be stored as a single string
              checkboxAnswers[questionId] = [savedAnswer];
            }
            // If savedAnswer is null, empty list, or empty string, don't set anything
            // This ensures optional checkbox questions remain unset if not answered
          }
        }
      }
    }
  }

  @override
  void onFormReset() {
    // Reset vertigo-specific state
    radioAnswers.clear();
    checkboxAnswers.clear();
  }
}
